# SMS Variable Mapping Modal

This component provides functionality for mapping SMS template variables to database fields.

## Features

- **Variable Detection**: Automatically detects `[Var]` placeholders in SMS template body
- **Click-to-Map**: Users can click on `[Var]` placeholders to open the mapping modal
- **Database Field Mapping**: Maps variables to specific database fields (e.g., `user.first_name`)
- **Suggested Fields**: Provides common database field suggestions for quick selection
- **Real-time Preview**: Shows how the mapped variable will appear in the template
- **Variable Counter**: Displays count of mapped vs unmapped variables

## Usage

1. **Select SMS Channel**: Choose SMS as the channel type in the create template form
2. **Add Variables**: Type your message with `[Var]` placeholders where you want dynamic content
   ```
   Example: "Dear customer [Var], Payment on your account [Var] is pending since [Var]. Please pay your dues of Rs [Var] to regularize account."
   ```
3. **Map Variables**: Click on any `[Var]` placeholder to open the mapping modal
4. **Choose Database Field**: Either type a custom field or select from suggested fields
5. **Confirm Mapping**: Click "Map" to replace `[Var]` with your chosen database field

## Example Transformation

**Before mapping:**
```
Dear customer [Var], Payment on your account [Var] is pending since [Var].
```

**After mapping:**
```
Dear customer [user.first_name], Payment on your account [account.number] is pending since [payment.due_date].
```

## Suggested Database Fields

The modal includes common database field suggestions:
- `user.first_name`
- `user.last_name`
- `user.email`
- `user.phone`
- `account.number`
- `account.balance`
- `payment.amount`
- `payment.due_date`
- `company.name`

## Technical Implementation

- **Component**: `VariableMappingModalComponent`
- **Modal Service**: Uses `ngx-bootstrap/modal`
- **Form Validation**: Reactive forms with validation
- **Styling**: Custom SCSS with responsive design
- **Integration**: Seamlessly integrated with the create template component
