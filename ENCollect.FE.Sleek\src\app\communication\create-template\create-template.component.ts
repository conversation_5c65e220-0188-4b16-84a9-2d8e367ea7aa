import { Component, inject, ViewChild, TemplateRef } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from "@angular/forms";
import { SharedModule } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";

@Component({
  selector: "app-create-template",
  standalone: true,
  imports: [FormsModule, SharedModule],
  templateUrl: "./create-template.component.html",
  styleUrl: "./create-template.component.scss",
})
export class CreateTemplateComponent {
  private fb: FormBuilder = inject(FormBuilder);
  private modalService: BsModalService = inject(BsModalService);
  modalRef?: BsModalRef;

  @ViewChild('variableMappingModal') variableMappingModal!: TemplateRef<any>;

  // Variable mapping form and data
  variableMappingForm!: FormGroup;
  currentVariable: string = '';
  currentStartPos: number = 0;
  currentEndPos: number = 0;
  currentLanguageIndex: number = 0;

  // Sample database field suggestions
  suggestedFields = [
    'user.first_name',
    'user.last_name',
    'user.email',
    'user.phone',
    'account.number',
    'account.balance',
    'payment.amount',
    'payment.due_date',
    'company.name'
  ];

  breadcrumbData = [
    { label: "Communication" },
    { label: "Create Communication Template" },
  ];
  allLanguages: any[] = [
    { name: "English", code: "en" },
    { name: "Hindi", code: "hi" },
    { name: "Tamil", code: "ta" },
    { name: "Bengali", code: "bn" },
    { name: "Telugu", code: "te" },
    { name: "Marathi", code: "mr" },
    { name: "Urdu", code: "ur" },
    { name: "Gujarati", code: "gu" },
    { name: "Kannada", code: "kn" },
    { name: "Malayalam", code: "ml" },
    { name: "Odia", code: "or" },
    { name: "Punjabi", code: "pa" },
    { name: "Assamese", code: "as" },
    { name: "Maithili", code: "mai" },
    { name: "Santali", code: "sat" },
    { name: "Kashmiri", code: "ks" },
    { name: "Konkani", code: "kok" },
    { name: "Sindhi", code: "sd" },
    { name: "Sanskrit", code: "sa" },
    { name: "Manipuri", code: "mni" },
    { name: "Bodo", code: "brx" },
    { name: "Dogri", code: "doi" },
  ];
  createForm!: FormGroup;

  constructor() {
    this.buildCreateTemplateForm();
    this.buildVariableMappingForm();
  }

  buildCreateTemplateForm() {
    this.createForm = this.fb.group({
      channelType: ["email"],
      templateName: [null, [Validators.required]],
      activeLanguage: [this.allLanguages[0].code],
      languages: this.buildLanguagesFormArray(),
    });
  }

  buildLanguagesFormArray(data?: any[]) {
    const formArray = new FormArray([]);
    data = data || [this.allLanguages[0]];
    data?.forEach((o) => {
      formArray.push(this.buildLanguageFormGroup(o));
    });
    return formArray;
  }

  buildLanguageFormGroup(data?: any) {
    return this.fb.group({
      languageCode: data?.code,
      languageName: data?.name,
      emailSubject: [null],
      templateBody: [null, [Validators.required]],
    });
  }

  buildVariableMappingForm() {
    this.variableMappingForm = this.fb.group({
      databaseField: ['', [Validators.required]]
    });
  }

  get fValue(): any {
    return this.createForm.value;
  }

  onTemplateBodyClick(event: MouseEvent, languageIndex: number) {
    // Only handle clicks for SMS channel type
    if (this.fValue?.channelType !== 'sms') return;

    const target = event.target as HTMLTextAreaElement;
    const text = target.value || '';
    const cursorPosition = target.selectionStart || 0;

    // Find if cursor is within a [Var] placeholder
    const varPattern = /\[Var\]/gi;
    let match;

    while ((match = varPattern.exec(text)) !== null) {
      const start = match.index;
      const end = match.index + match[0].length;

      if (cursorPosition >= start && cursorPosition <= end) {
        this.openVariableMappingModal(match[0], start, end, languageIndex);
        break;
      }
    }
  }

  openVariableMappingModal(variable: string, startPos: number, endPos: number, languageIndex: number) {
    // Store current mapping context
    this.currentVariable = variable;
    this.currentStartPos = startPos;
    this.currentEndPos = endPos;
    this.currentLanguageIndex = languageIndex;

    // Reset form
    this.variableMappingForm.reset();

    // Open modal
    this.modalRef = this.modalService.show(this.variableMappingModal, {
      class: 'modal-dialog-centered',
      ignoreBackdropClick: true
    });
  }

  updateTemplateBodyWithMapping(mappedField: string, startPos: number, endPos: number, languageIndex: number) {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const languageGroup = languagesArray.at(languageIndex);
    const templateBodyControl = languageGroup.get('templateBody');

    if (templateBodyControl) {
      const currentText = templateBodyControl.value || '';
      const newText = currentText.substring(0, startPos) +
                     `[${mappedField}]` +
                     currentText.substring(endPos);
      templateBodyControl.setValue(newText);
    }
  }

  getVariableCount(templateBody: string): number {
    if (!templateBody) return 0;
    const varPattern = /\[Var\]/gi;
    const matches = templateBody.match(varPattern);
    return matches ? matches.length : 0;
  }

  getMappedVariableCount(templateBody: string): number {
    if (!templateBody) return 0;
    // Count variables that are not [Var] (i.e., already mapped)
    const allVarPattern = /\[[^\]]+\]/g;
    const unmappedVarPattern = /\[Var\]/gi;

    const allMatches = templateBody.match(allVarPattern);
    const unmappedMatches = templateBody.match(unmappedVarPattern);

    const totalVars = allMatches ? allMatches.length : 0;
    const unmappedVars = unmappedMatches ? unmappedMatches.length : 0;

    return totalVars - unmappedVars;
  }



  onHeaderUpload(event: any) {
    // Handle header upload for letter type
  }

  onFooterUpload(event: any) {
    // Handle footer upload for letter type
  }

  // Template-based modal methods
  useSuggestedField(field: string) {
    this.variableMappingForm.patchValue({
      databaseField: field
    });
  }

  mapVariableFromTemplate() {
    if (this.variableMappingForm.valid) {
      const mappedField = this.variableMappingForm.get('databaseField')?.value;
      this.updateTemplateBodyWithMapping(mappedField, this.currentStartPos, this.currentEndPos, this.currentLanguageIndex);
      this.closeVariableModal();
    }
  }

  closeVariableModal() {
    this.modalRef?.hide();
    this.variableMappingForm.reset();
  }
}
