.sms-template {
  cursor: text;
  position: relative;

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  // Add a subtle background to indicate SMS template
  background-color: #f8f9fa;
  border-left: 3px solid #28a745;
}

.form-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;

  &.text-muted {
    color: #6c757d;
  }

  &.text-info {
    color: #17a2b8;
    font-weight: 500;
  }
}

// Style for variable placeholders in SMS templates
.variable-placeholder {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #bbdefb;

  &:hover {
    background-color: #bbdefb;
    border-color: #90caf9;
  }
}