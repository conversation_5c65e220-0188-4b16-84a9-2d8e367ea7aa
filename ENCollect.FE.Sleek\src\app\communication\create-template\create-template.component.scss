.sms-template {
  cursor: text;
  position: relative;

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  // Add a subtle background to indicate SMS template
  background-color: #f8f9fa;
  border-left: 3px solid #28a745;

  // Add a note about clickable variables
  &::after {
    content: "💡 Click on [Var] to map variables";
    position: absolute;
    top: -25px;
    right: 5px;
    font-size: 0.75rem;
    color: #28a745;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #28a745;
    pointer-events: none;
  }
}

.form-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;

  &.text-muted {
    color: #6c757d;
  }

  &.text-info {
    color: #17a2b8;
    font-weight: 500;
  }
}

// Style for variable placeholders in SMS templates
.variable-placeholder {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #bbdefb;

  &:hover {
    background-color: #bbdefb;
    border-color: #90caf9;
  }
}

// Modal styles
.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;

  .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
  }

  .btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;

    &:hover {
      color: #000;
    }
  }
}

.modal-body {
  padding: 1.5rem;

  .form-control-group {
    margin-bottom: 1rem;

    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #495057;

      &.required::after {
        content: " *";
        color: #dc3545;
      }
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ced4da;
      border-radius: 0.375rem;
      font-size: 0.875rem;

      &:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      &.is-invalid {
        border-color: #dc3545;
      }
    }

    .invalid-feedback {
      display: block;
      width: 100%;
      margin-top: 0.25rem;
      font-size: 0.75rem;
      color: #dc3545;
    }
  }

  .suggested-fields {
    .form-label {
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
    }

    .suggested-fields-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;

        &:hover {
          background-color: #e9ecef;
          border-color: #adb5bd;
        }
      }
    }
  }

  .bg-light {
    background-color: #f8f9fa !important;
  }
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;

    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      color: #fff;

      &:hover:not(:disabled) {
        background-color: #5a6268;
        border-color: #545b62;
      }

      &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
      }
    }

    &.btn-outline-secondary {
      color: #6c757d;
      border-color: #6c757d;
      background-color: transparent;

      &:hover {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
      }
    }
  }
}