<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <h2 class="title">Create Communication Template</h2>
  <div class="enc-card" [formGroup]="createForm">
    <div class="card-header border-bottom-0">
      <div>
        <h3>Create Communication Template</h3>
        <h5 class="mt-3">
          Design templates for different communication channels with variable
          support and multilingual capabilities
        </h5>
      </div>
    </div>
    <div class="card-content">
      <div class="row">
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Channel Type</label>
            <div class="form-radio-group">
              <label>
                <input
                  type="radio"
                  name="channel-type"
                  id="channelTypeEmail"
                  [value]="'email'"
                  formControlName="channelType"
                />
                Email
              </label>
              <label>
                <input
                  type="radio"
                  name="channel-type"
                  id="channelTypeSMS"
                  [value]="'sms'"
                  formControlName="channelType"
                />
                SMS
              </label>
              <label>
                <input
                  type="radio"
                  name="channel-type"
                  id="channelTypeLetter"
                  [value]="'letter'"
                  formControlName="channelType"
                />
                Letter
              </label>
            </div>
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Name</label>
            <input
              type="text"
              class="form-control"
              placeholder="Enter Template Name"
              name="template-name"
              id="templateName"
              formControlName="templateName"
            />
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Language</label>
            <div class="d-flex align-items-center justify-content-between">
              <div class="form-button-group" btnRadioGroup>
                @for (lang of fValue?.languages; track lang.languageCode) {
                <button
                  [btnRadio]="lang?.languageCode"
                  name="template-language"
                  id="template-language-{{ lang?.languageCode }}"
                  formControlName="activeLanguage"
                >
                  {{ lang?.languageName }}
                </button>
                }
              </div>
              <button class="btn btn-outline-primary" id="addLanguageBtn">
                <svg-icon
                  src="assets/new/svgs/language.svg"
                  class="me-2"
                ></svg-icon>
                <span>Add Language</span>
              </button>
            </div>
          </div>
        </div>
<!-- Show only if "Letter" is selected -->
<div class ="row">
<div class="col-md-6" *ngIf="createForm?.get('channelType')?.value === 'letter'">
  <div class="form-control-group">
    <label class="form-label required">Upload Header</label>
    <input
      type="file"
      class="form-control"
      accept=".jpg,.jpeg,.png,.pdf"
      (change)="onHeaderUpload($event)"
    />
  </div>
</div>

<div class="col-md-6" *ngIf="createForm?.get('channelType')?.value === 'letter'">
  <div class="form-control-group">
    <label class="form-label required">Upload Footer</label>
    <input
      type="file"
      class="form-control"
      accept=".jpg,.jpeg,.png,.pdf"
      (change)="onFooterUpload($event)"
    />
  </div>
</div>
</div>

        @for (lang of fValue?.languages; let i = $index; track
        lang.languageCode) {
        <ng-container [formArrayName]="'languages'">
          <ng-container [formGroupName]="i">
            @if (fValue?.channelType === 'email') {
            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label required">Subject Line</label>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Enter Email Subject"
                  name="email-subject"
                  id="emailSubject"
                  formControlName="emailSubject"
                />
              </div>
            </div>
            }

            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label required">Template Body</label>
                <textarea
                  class="form-control"
                  [class.sms-template]="fValue?.channelType === 'sms'"
                  placeholder="Enter Approved Template Content. For SMS, use [Var] for variables that can be clicked to map to database fields."
                  name="template-body"
                  id="templateBody"
                  rows="7"
                  formControlName="templateBody"
                  (click)="onTemplateBodyClick($event, i)"
                ></textarea>
                <div class="mt-2" *ngIf="fValue?.channelType === 'sms'">
                  <small class="form-text text-muted d-block">
                    Click on [Var] placeholders to map them to database fields
                  </small>
                  <small class="form-text text-info d-block mt-1"
                         *ngIf="fValue?.languages?.[i]?.templateBody">
                    Variables: {{ getMappedVariableCount(fValue?.languages?.[i]?.templateBody) }} mapped,
                    {{ getVariableCount(fValue?.languages?.[i]?.templateBody) }} unmapped
                  </small>

                </div>
              </div>
            </div>
          </ng-container>
        </ng-container>
        }
      </div>
    </div>
    <div class="card-footer">
      <button class="btn btn-secondary mw-150px me-4">Create Template</button>
      <button class="btn btn-outline-primary mw-150px">Cancel</button>
    </div>
  </div>
</div>

<!-- Variable Mapping Modal Template -->
<ng-template #variableMappingModal>
  <div class="modal-header">
    <h4 class="modal-title">Map Variable to Database Field</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="closeVariableModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <p class="text-muted mb-3">Specify which database field this variable should use</p>

    <form [formGroup]="variableMappingForm">
      <div class="form-control-group">
        <label class="form-label required">Database Field</label>
        <input
          type="text"
          class="form-control"
          placeholder="e.g., user.first_name"
          formControlName="databaseField"
          autocomplete="off"
        />
        <div class="invalid-feedback" *ngIf="variableMappingForm.get('databaseField')?.invalid && variableMappingForm.get('databaseField')?.touched">
          Database field is required
        </div>
      </div>

      <!-- Suggested Fields -->
      <div class="suggested-fields mt-3">
        <label class="form-label">Suggested Fields:</label>
        <div class="suggested-fields-container">
          <button
            type="button"
            class="btn btn-outline-secondary btn-sm me-2 mb-2"
            *ngFor="let field of suggestedFields"
            (click)="useSuggestedField(field)"
          >
            {{ field }}
          </button>
        </div>
      </div>
    </form>

    <!-- Preview section -->
    <div class="mt-3 p-3 bg-light rounded">
      <small class="text-muted">Preview:</small>
      <div class="mt-1">
        <span class="text-success me-2">{{ currentVariable }}</span>
        <span class="text-muted">→</span>
        <span class="text-primary ms-2" *ngIf="variableMappingForm.get('databaseField')?.value">
          [{{ variableMappingForm.get('databaseField')?.value }}]
        </span>
        <span class="text-muted ms-2" *ngIf="!variableMappingForm.get('databaseField')?.value">
          [database_field]
        </span>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-secondary me-2"
      (click)="mapVariableFromTemplate()"
      [disabled]="variableMappingForm.invalid"
    >
      Map
    </button>
    <button type="button" class="btn btn-outline-secondary" (click)="closeVariableModal()">
      Cancel
    </button>
  </div>
</ng-template>
